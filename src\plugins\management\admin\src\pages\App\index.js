const React = require('react');
const { useState } = React;
const { Routes, Route } = require('react-router-dom');
const Sidebar = require('../../components/Sidebar');
const DashboardHeader = require('../../components/DashboardHeader');

// Import Be Vietnam Pro font
require('@fontsource/be-vietnam-pro/100.css');
require('@fontsource/be-vietnam-pro/200.css');
require('@fontsource/be-vietnam-pro/300.css');
require('@fontsource/be-vietnam-pro/400.css');
require('@fontsource/be-vietnam-pro/500.css');
require('@fontsource/be-vietnam-pro/600.css');
require('@fontsource/be-vietnam-pro/700.css');
require('@fontsource/be-vietnam-pro/800.css');
require('@fontsource/be-vietnam-pro/900.css');

// Import global styles
require('../../styles/global.css');

// Import components
const ProductManagement = require('../../components/ProductManagement');
const OrderManagement = require('../../components/OrderManagement');
const CategoryManagement = require('../../components/CategoryManagement');
const BrandManagement = require('../../components/BrandManagement');
const NewsCategories = require('../../components/NewsCategories');
const NewsManagement = require('../../components/NewsManagement');

// Import pages
const WithdrawalList = require('../../components/WithdrawalList');
const AffiliateOverview = require('../../components/AffiliateOverview');
const PromotionManagement = require('../../components/PromotionManagement');
const UserList = require('../../components/UserList');
const Settings = require('../../components/Settings');
const CommissionList = require('../../components/CommissionList');
const Dashboard = require('../../components/Dashboard');

// TODO: Create detail components later
// const OrderDetail = require('../../components/OrderDetail').default;
// const UserDetail = require('../../components/UserDetail').default;

// Simple NotFound component
const NotFound = () => {
  return React.createElement(
    'div',
    {
      style: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '50vh',
        fontFamily: 'Be Vietnam Pro, sans-serif',
      },
    },
    [
      React.createElement(
        'h1',
        {
          key: 'h1',
          style: { fontSize: '48px', color: '#6b7280', margin: '0 0 16px 0' },
        },
        '404'
      ),
      React.createElement(
        'p',
        {
          key: 'p',
          style: { fontSize: '18px', color: '#9ca3af', margin: 0 },
        },
        'Trang không tìm thấy'
      ),
    ]
  );
};

// Import global CSS for management container styles
require('../../styles/global.css');

// Helper function to create management container
const ManagementContainer = (props) => {
  const { children, ...restProps } = props;
  return React.createElement(
    'div',
    {
      className: 'management-container',
      ...restProps,
    },
    children
  );
};

const App = () => {
  // Lấy trạng thái collapsed từ localStorage, mặc định là false
  const [collapsed, setCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebar-collapsed');
    return saved ? JSON.parse(saved) : false;
  });

  // Lưu trạng thái collapsed vào localStorage khi thay đổi
  const toggleCollapsed = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed));
  };

  return React.createElement(
    ManagementContainer,
    { className: 'management-plugin' },
    React.createElement(
      'div',
      { style: { display: 'flex', minHeight: '100vh' } },
      [
        React.createElement(Sidebar, { key: 'sidebar', collapsed }),

        // Toggle Button
        React.createElement(
          'button',
          {
            key: 'toggle-button',
            onClick: toggleCollapsed,
            style: {
              position: 'fixed',
              top: '20px',
              left: collapsed ? '80px' : '265px',
              zIndex: 1000,
              background: '#ffffff',
              border: '1px solid #e2e8f0',
              borderRadius: '8px',
              padding: '8px',
              cursor: 'pointer',
              transition:
                'left 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease, transform 0.2s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '32px',
              height: '32px',
            },
          },
          React.createElement(
            'span',
            {
              style: {
                fontSize: '12px',
                color: '#6b7280',
                transition: 'transform 0.3s ease',
                transform: collapsed ? 'rotate(0deg)' : 'rotate(180deg)',
              },
            },
            '→'
          )
        ),

        React.createElement(
          'div',
          {
            key: 'main-content',
            style: {
              marginLeft: collapsed ? '10px' : '200px',
              transition: 'margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              flex: 1,
              minHeight: '100vh',
              background: '#f8fafc',
            },
          },
          [
            React.createElement(DashboardHeader, { key: 'header', collapsed }),

            React.createElement(
              'div',
              {
                key: 'content',
                style: {
                  paddingTop: '80px', // Space for fixed header
                },
              },
              React.createElement(Routes, null, [
                React.createElement(Route, {
                  key: 'index',
                  index: true,
                  element: React.createElement(Dashboard),
                }),
                React.createElement(Route, {
                  key: 'dashboard',
                  path: 'dashboard',
                  element: React.createElement(Dashboard),
                }),
                React.createElement(Route, {
                  key: 'orders',
                  path: 'orders',
                  element: React.createElement(OrderManagement),
                }),
                React.createElement(Route, {
                  key: 'users',
                  path: 'users',
                  element: React.createElement(UserList),
                }),
                React.createElement(Route, {
                  key: 'commissions',
                  path: 'commissions',
                  element: React.createElement(CommissionList),
                }),
                React.createElement(Route, {
                  key: 'withdrawals',
                  path: 'affiliate/withdrawals',
                  element: React.createElement(WithdrawalList),
                }),
                React.createElement(Route, {
                  key: 'affiliate-overview',
                  path: 'affiliate/overview',
                  element: React.createElement(AffiliateOverview),
                }),
                React.createElement(Route, {
                  key: 'news-categories',
                  path: 'news/categories',
                  element: React.createElement(NewsCategories),
                }),
                React.createElement(Route, {
                  key: 'news-articles',
                  path: 'news/articles',
                  element: React.createElement(NewsManagement),
                }),
                React.createElement(Route, {
                  key: 'product-categories',
                  path: 'products/categories',
                  element: React.createElement(CategoryManagement),
                }),
                React.createElement(Route, {
                  key: 'product-brands',
                  path: 'products/brands',
                  element: React.createElement(BrandManagement),
                }),
                React.createElement(Route, {
                  key: 'product-list',
                  path: 'products/list',
                  element: React.createElement(ProductManagement),
                }),
                React.createElement(Route, {
                  key: 'promotions',
                  path: 'products/promotions',
                  element: React.createElement(PromotionManagement),
                }),
                React.createElement(Route, {
                  key: 'settings',
                  path: 'settings/*',
                  element: React.createElement(Settings),
                }),
                React.createElement(Route, {
                  key: 'not-found',
                  path: '*',
                  element: React.createElement(NotFound),
                }),
              ])
            ),
          ]
        ),
      ]
    )
  );
};

module.exports = App;

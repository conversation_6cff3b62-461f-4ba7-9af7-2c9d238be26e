const React = require('react');
const { useState, useEffect, useRef } = React;
const { useNavigate, useLocation } = require('react-router-dom');
const { Modal, Tooltip } = require('antd');
const { ExclamationCircleOutlined } = require('@ant-design/icons');
const {
  Home,
  ChevronDown,
  Package,
  Layers,
  Database,
  User,
  StickyNoteIcon,
  StickyNote,
  Users,
  Settings,
  LogOut,
  LayoutGrid,
  ShoppingCart,
} = require('lucide-react');

const Sidebar = ({ collapsed }) => {
  // const navigate = useNavigate();
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState(['management']);
  const [animatingMenus, setAnimatingMenus] = useState([]);
  const submenuRefs = useRef({});
  const [submenuHeights, setSubmenuHeights] = useState({});
  const [hoveredMenu, setHoveredMenu] = useState(null);

  const handleExpandMenu = (menu) => {
    setExpandedMenus((prev) => [...prev, menu]);
    setTimeout(() => {
      const el = submenuRefs.current[menu];
      if (el) {
        setSubmenuHeights((prev) => ({
          ...prev,
          [menu]: el.scrollHeight,
        }));
      }
    }, 0); // Đợi DOM cập nhật
  };

  const handleCollapseMenu = (menu) => {
    setAnimatingMenus((prev) => [...prev, menu]);
    setSubmenuHeights((prev) => ({
      ...prev,
      [menu]: 0,
    }));
    setTimeout(() => {
      setExpandedMenus((prev) => prev.filter((m) => m !== menu));
      setAnimatingMenus((prev) => prev.filter((m) => m !== menu));
    }, 150);
  };

  const toggleMenu = (menu) => {
    const isExpanded = expandedMenus.includes(menu);
    if (isExpanded) {
      handleCollapseMenu(menu);
    } else {
      handleExpandMenu(menu);
    }
  };

  // Render submenu content for tooltip when collapsed
  const renderSubmenuTooltip = (children) =>
    React.createElement(
      'div',
      {
        style: {
          background: '#ffffff',
          borderRadius: '8px',
          padding: '8px 0',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          border: '1px solid #e2e8f0',
          minWidth: '180px',
        },
      },
      children.map((child, index) =>
        React.createElement(
          'button',
          {
            key: index,
            onClick: () => handleMenuClick(child.path),
            style: {
              width: '100%',
              padding: '8px 16px',
              border: 'none',
              background: 'transparent',
              textAlign: 'left',
              cursor: 'pointer',
              fontSize: '14px',
              fontFamily: "'Be Vietnam Pro', sans-serif",
              color: getSelectedKey() === child.key ? '#3b82f6' : '#374151',
              fontWeight: getSelectedKey() === child.key ? '500' : '400',
              transition: 'all 0.2s ease',
            },
            onMouseEnter: (e) => {
              if (getSelectedKey() !== child.key) {
                e.currentTarget.style.backgroundColor = '#f8fafc';
                e.currentTarget.style.color = '#3b82f6';
              }
            },
            onMouseLeave: (e) => {
              if (getSelectedKey() !== child.key) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#374151';
              }
            },
          },
          child.title
        )
      )
    );

  const getSelectedKey = () => {
    const path = location.pathname;
    if (path.includes('/dashboard')) return 'dashboard';
    if (path.includes('/orders')) return 'orders';
    if (path.includes('/news/categories')) return 'news-categories';
    if (path.includes('/news/articles')) return 'news-articles';
    if (path.includes('/products/categories')) return 'products-categories';
    if (path.includes('/products/list')) return 'products-list';
    if (path.includes('/products/management')) return 'products-management';
    if (path.includes('/products/promotions')) return 'products-promotions';
    if (path.includes('/commissions')) return 'commissions';
    if (path.includes('/withdrawals')) return 'withdrawals';
    if (path.includes('/users')) return 'users-list';
    if (path.includes('/affiliate/overview')) return 'affiliate-overview';
    if (path.includes('/affiliate/commission-policy'))
      return 'affiliate-commission-policy';
    if (path.includes('/affiliate/withdrawals')) return 'affiliate-withdrawals';
    if (path.includes('/settings')) return 'settings';
    if (path.includes('/logout')) return 'logout';
    return 'dashboard';
  };

  // Get parent menu that should be expanded based on current path
  const getParentMenuToExpand = () => {
    const path = location.pathname;
    if (path.includes('/news/')) return 'news';
    if (path.includes('/products/')) return 'products';
    if (
      path.includes('/users/') ||
      path.includes('/commissions') ||
      path.includes('/withdrawals')
    )
      return 'users';
    if (path.includes('/affiliate/')) return 'affiliate';
    return null;
  };

  // Auto expand parent menu based on current path
  useEffect(() => {
    const parentMenu = getParentMenuToExpand();
    if (parentMenu && !expandedMenus.includes(parentMenu)) {
      handleExpandMenu(parentMenu);
    }
  }, [location.pathname]);

  const handleMenuClick = (path, menuId) => {
    if (menuId === 'logout') {
      // Hiển thị modal confirm đẹp
      Modal.confirm({
        title: 'Xác nhận đăng xuất',
        icon: React.createElement(ExclamationCircleOutlined, {
          style: { color: '#faad14' },
        }),
        content: 'Bạn có chắc chắn muốn đăng xuất khỏi hệ thống không?',
        okText: 'Đăng xuất',
        cancelText: 'Hủy',
        okType: 'danger',
        centered: true,
        maskClosable: true,
        onOk() {
          // Xóa tất cả dữ liệu xác thực
          localStorage.clear();
          sessionStorage.clear();

          // Xóa cookies nếu có
          document.cookie.split(';').forEach((c) => {
            const eqPos = c.indexOf('=');
            const name = eqPos > -1 ? c.substring(0, eqPos) : c;
            document.cookie =
              name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
          });

          // Force reload và redirect về root, middleware sẽ xử lý redirect
          window.location.replace('/');
        },
        onCancel() {
          // Không làm gì cả
        },
      });
    } else {
      // navigate(path);
    }
  };

  const menuItems = [
    {
      id: 'dashboard',
      title: 'Bảng điều khiển',
      icon: LayoutGrid,
      path: 'dashboard',
      key: 'dashboard',
    },
    {
      id: 'orders',
      title: 'Quản lý đơn hàng',
      icon: ShoppingCart,
      path: 'orders',
      key: 'orders',
    },
    {
      id: 'news',
      title: 'Quản lý tin tức',
      icon: StickyNote,
      children: [
        {
          title: 'Tin tức',
          path: 'news/articles',
          key: 'news-articles',
        },
        {
          title: 'Danh mục tin tức',
          path: 'news/categories',
          key: 'news-categories',
        },
      ],
    },
    {
      id: 'products',
      title: 'Quản lý sản phẩm',
      icon: Package,
      children: [
        {
          title: 'Danh mục sản phẩm',
          path: 'products/categories',
          key: 'products-categories',
        },
        {
          title: 'Sản phẩm',
          path: 'products/list',
          key: 'products-list',
        },
        {
          title: 'Thương hiệu',
          path: 'products/brands',
          key: 'products-brands',
        },
        {
          title: 'Khuyến mãi',
          path: 'products/promotions',
          key: 'products-promotions',
        },
      ],
    },
    {
      id: 'users',
      title: 'Quản lý đại lý',
      icon: User,
      children: [
        {
          title: 'Danh sách đại lý',
          path: 'users',
          key: 'users-list',
        },
        {
          title: 'Lịch sử hoa hồng',
          path: 'commissions',
          key: 'commissions',
        },
      ],
    },
    {
      id: 'affiliate',
      title: 'Affiliate',
      icon: Users,
      children: [
        {
          title: 'Tổng quan',
          path: 'affiliate/overview',
          key: 'affiliate-overview',
        },
        {
          title: 'Yêu cầu rút tiền',
          path: 'affiliate/withdrawals',
          key: 'affiliate-withdrawals',
        },
      ],
    },
    {
      id: 'settings',
      title: 'Cài đặt',
      icon: Settings,
      path: 'settings',
      key: 'settings',
    },
    {
      id: 'logout',
      title: 'Đăng xuất',
      icon: LogOut,
      path: 'logout',
      key: 'logout',
    },
  ];

  return React.createElement(
    'div',
    { className: `sidebar ${collapsed ? 'collapsed' : 'expanded'}` },
    [
      // Logo
      React.createElement(
        'div',
        { key: 'logo', className: 'sidebar-logo' },
        React.createElement(
          'div',
          { className: 'sidebar-logo-content' },
          React.createElement('img', {
            src: '/uploads/logo.jpg',
            alt: 'Logo',
            className: 'sidebar-logo-image',
          })
        )
      ),

      // Menu Items
      React.createElement(
        'div',
        { key: 'menu', className: 'sidebar-menu' },
        menuItems.map((item) =>
          React.createElement(
            'div',
            { key: item.id, className: 'sidebar-menu-item' },
            [
              collapsed
                ? React.createElement(
                    Tooltip,
                    {
                      key: 'tooltip',
                      title: item.children
                        ? renderSubmenuTooltip(item.children)
                        : item.title,
                      placement: 'right',
                      trigger: 'hover',
                      rootClassName: item.children
                        ? 'sidebar-submenu-tooltip'
                        : 'sidebar-menu-tooltip',
                      mouseEnterDelay: 0.1,
                      mouseLeaveDelay: 0.1,
                    },
                    React.createElement(
                      'button',
                      {
                        onClick: () =>
                          !item.children && handleMenuClick(item.path, item.id),
                        className: `sidebar-menu-button ${
                          !item.children && getSelectedKey() === item.key
                            ? 'active'
                            : ''
                        }`,
                      },
                      React.createElement(
                        'div',
                        { className: 'sidebar-menu-button-content' },
                        React.createElement(item.icon, {
                          className: 'sidebar-menu-button-icon',
                        })
                      )
                    )
                  )
                : React.createElement(
                    'button',
                    {
                      key: 'button',
                      onClick: () =>
                        item.children
                          ? toggleMenu(item.id)
                          : handleMenuClick(item.path, item.id),
                      className: `sidebar-menu-button ${
                        !item.children && getSelectedKey() === item.key
                          ? 'active'
                          : ''
                      }`,
                    },
                    [
                      React.createElement(
                        'div',
                        {
                          key: 'content',
                          className: 'sidebar-menu-button-content',
                        },
                        [
                          React.createElement(item.icon, {
                            key: 'icon',
                            className: 'sidebar-menu-button-icon',
                          }),
                          React.createElement(
                            'span',
                            {
                              key: 'text',
                              className: 'sidebar-menu-button-text',
                            },
                            item.title
                          ),
                        ]
                      ),
                      item.children &&
                        React.createElement(ChevronDown, {
                          key: 'chevron',
                          className: `sidebar-chevron ${
                            expandedMenus.includes(item.id) ? 'rotated' : ''
                          }`,
                        }),
                    ]
                  ),

              !collapsed &&
                (expandedMenus.includes(item.id) ||
                  animatingMenus.includes(item.id)) &&
                item.children &&
                React.createElement(
                  'div',
                  {
                    key: 'submenu',
                    ref: (el) => {
                      if (el) submenuRefs.current[item.id] = el;
                    },
                    className: `sidebar-submenu ${
                      expandedMenus.includes(item.id)
                        ? 'expanded'
                        : 'collapsing'
                    }`,
                    style: {
                      '--submenu-height': `${submenuHeights[item.id] || 0}px`,
                    },
                  },
                  item.children.map((child, index) =>
                    React.createElement(
                      'button',
                      {
                        key: index,
                        onClick: () => handleMenuClick(child.path),
                        className: `sidebar-submenu-item ${
                          getSelectedKey() === child.key ? 'active' : ''
                        }`,
                      },
                      child.title
                    )
                  )
                ),
            ].filter(Boolean)
          )
        )
      ),
    ]
  );
};

module.exports = Sidebar;

const React = require('react');
const { useState, useEffect, useRef } = React;
const { Search, Bell, User, Package, Clock } = require('lucide-react');
const { Badge, Spin, Empty } = require('antd');
const { useFetchClient } = require('@strapi/helper-plugin');
const { useNavigate } = require('react-router-dom');

// Import CSS
require('./DashboardHeader.css');

const DashboardHeader = ({ collapsed = false }) => {
  const { get } = useFetchClient();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Fetch notifications
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      // Fetch pending orders
      const { data: ordersData } = await get('/management/orders', {
        params: { status: 'Chờ xác nhận', pageSize: 10 },
      });

      const pendingOrders = ordersData?.data || [];

      // Create notifications from pending orders
      const orderNotifications = pendingOrders.map((order) => ({
        id: `order-${order.id}`,
        type: 'order',
        title: 'Đơn hàng mới',
        message: `Đơn hàng ${order.code} cần được xác nhận`,
        time: formatTimeAgo(order.createdAt),
        data: order,
      }));

      setNotifications(orderNotifications);
      setNotificationCount(orderNotifications.length);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return 'Vừa xong';
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} giờ trước`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} ngày trước`;
  };

  // Handle notification click
  const handleNotificationClick = (notification) => {
    if (notification.type === 'order') {
      navigate(`orders`);
    }
    setDropdownVisible(false);
  };

  // Fetch notifications on component mount and set up polling
  useEffect(() => {
    fetchNotifications();

    // Poll for new notifications every 30 seconds
    const interval = setInterval(fetchNotifications, 30000);

    return () => clearInterval(interval);
  }, []);

  // Handle click outside to close dropdown
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownVisible(false);
      }
    };

    if (dropdownVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownVisible]);

  return React.createElement(
    'div',
    {
      className: `dashboard-header ${collapsed ? 'collapsed' : ''}`,
    },
    React.createElement('div', { className: 'header-content' }, [
      React.createElement('div', { key: 'left', className: 'left-section' }),

      React.createElement('div', { key: 'right', className: 'right-section' }, [
        React.createElement(
          'div',
          { key: 'search', className: 'search-container' },
          [
            React.createElement(Search, {
              key: 'search-icon',
              className: 'search-icon',
            }),
            React.createElement('input', {
              key: 'search-input',
              className: 'search-input',
              type: 'text',
              placeholder: 'Search...',
            }),
          ]
        ),

        React.createElement(
          'div',
          {
            key: 'notifications',
            className: 'notification-container',
            ref: dropdownRef,
          },
          [
            React.createElement(
              'button',
              {
                key: 'notification-button',
                className: 'notification-button',
                onClick: () => setDropdownVisible(!dropdownVisible),
              },
              React.createElement(
                Badge,
                {
                  count: notificationCount,
                  size: 'small',
                  offset: [-2, 2],
                },
                React.createElement(Bell, { className: 'bell-icon' })
              )
            ),

            React.createElement(
              'div',
              {
                key: 'dropdown',
                className: `notification-dropdown ${
                  !dropdownVisible ? 'hidden' : ''
                }`,
              },
              [
                React.createElement(
                  'div',
                  { key: 'header', className: 'notification-header' },
                  React.createElement(
                    'h4',
                    { className: 'notification-title' },
                    `Thông báo (${notificationCount})`
                  )
                ),

                loading
                  ? React.createElement(
                      'div',
                      {
                        key: 'loading',
                        className: 'loading-container',
                      },
                      React.createElement(Spin, { size: 'small' })
                    )
                  : notifications.length > 0
                  ? React.createElement(
                      'div',
                      { key: 'list', className: 'notification-list' },
                      notifications.map((notification) =>
                        React.createElement(
                          'div',
                          {
                            key: notification.id,
                            className: 'notification-item',
                            onClick: () =>
                              handleNotificationClick(notification),
                          },
                          React.createElement(
                            'div',
                            { className: 'notification-content' },
                            [
                              React.createElement(
                                'div',
                                { key: 'icon', className: 'notification-icon' },
                                notification.type === 'order' &&
                                  React.createElement(Package, {
                                    size: 16,
                                    color: '#1890ff',
                                  }),
                                notification.type === 'user' &&
                                  React.createElement(User, {
                                    size: 16,
                                    color: '#52c41a',
                                  }),
                                notification.type === 'withdrawal' &&
                                  React.createElement(Clock, {
                                    size: 16,
                                    color: '#faad14',
                                  })
                              ),
                              React.createElement(
                                'div',
                                { key: 'text', className: 'notification-text' },
                                [
                                  React.createElement(
                                    'div',
                                    {
                                      key: 'message',
                                      className: 'notification-message',
                                    },
                                    notification.message
                                  ),
                                  React.createElement(
                                    'div',
                                    {
                                      key: 'time',
                                      className: 'notification-time',
                                    },
                                    notification.time
                                  ),
                                ]
                              ),
                            ]
                          )
                        )
                      )
                    )
                  : React.createElement(
                      'div',
                      { key: 'empty', className: 'notification-empty' },
                      React.createElement(Empty, {
                        image: Empty.PRESENTED_IMAGE_SIMPLE,
                        description: 'Không có thông báo mới',
                      })
                    ),
              ]
            ),
          ]
        ),

        React.createElement('div', { key: 'user', className: 'user-section' }, [
          React.createElement(
            'div',
            { key: 'avatar', className: 'user-avatar' },
            React.createElement(User, { className: 'user-icon' })
          ),
          React.createElement(
            'span',
            { key: 'name', className: 'user-name' },
            'Admin'
          ),
        ]),
      ]),
    ])
  );
};

module.exports = DashboardHeader;
